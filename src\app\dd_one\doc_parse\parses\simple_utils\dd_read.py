import asyncio
import json
import os
import re
from openpyxl import Workbook
import pandas as pd
from typing import List, Dict, Optional, Any, Tuple
from app.dd_one.model_serve.model_runtime.model_providers.model_interface import UnifiedLLMInterface
from app.dd_one.model_serve.model_runtime.entities import PromptMessage
from docx import Document
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from config.config import qwen_llm_config, style_json_path
from app.dd_one.doc_parse.parses.simple_utils.utils.orig_sheet_reader import process_excel_file
from app.dd_one.doc_parse.parses.simple_utils.utils.process_get_excel import process_excel, excel_coordinate_to_a1
from app.dd_one.doc_parse.parses.simple_utils.dd_utils.convert_doc import convert_doc_to_docx, accept_all_revisions
from app.dd_one.doc_parse.parses.simple_utils.dd_utils.dd_prompt import _project_prompt, _get_all_site_prompt, \
    extract_json_from_response
from pathlib import Path

llm_interface = UnifiedLLMInterface()


# 先按照大块进行切分
def split_by_parts(text: str, part_configs: List[Tuple[str, List[str]]] = None) -> Dict[str, str]:
    """
    将文本按配置的分部分规则分割，通过记录行号区间提取内容。

    Args:
        text: 输入文本
        part_configs: 每个部分的配置，格式为 [(part_name, [pattern1, pattern2, ...]), ...]
                     如果未提供，则使用默认配置

    Returns:
        Dict[str, str]: 每个部分的名称及其内容的字典
    """
    # print(text)
    # 默认配置
    default_configs = [
        ('文章上部分', [r'^\s*$']),  # 匹配文本开头（空行或文本开始）
        ('第一部分', [r'第[一]部分', '^一、一般说明']),
        ('第二部分', [r'^第[二]部分：', '^二、具体说明']),
        ('第三部分', [r'^第[三]部分', r'^三、指标释义', r'第三部分：具体说明及核对关系']),
        ('第四部分',
         [r'第[四]部分', r'^核对关系：', r'^核对关系', r'^第六部分：核对关系', r'^【核对关系】', r'^三、核对关系']),
        ('第五部分', [r'第[五]部分'])
    ]

    # 使用提供的配置或默认配置
    configs = part_configs if part_configs else default_configs

    # 编译所有正则表达式
    compiled_configs = [(name, [re.compile(pattern) for pattern in patterns])
                        for name, patterns in configs]

    # 分割文本为行
    lines = text.split('\n')

    # 记录每个部分的起始行号和名称
    part_boundaries = []
    for i, line in enumerate(lines):
        stripped_line = line.strip()
        for part_name, patterns in compiled_configs:
            if part_name == '文章上部分':
                continue  # “文章上部分”由默认逻辑处理
            for pattern in patterns:
                if pattern.search(stripped_line):
                    part_boundaries.append((part_name, i))
                    break

    # 添加文本末尾作为边界
    part_boundaries.append((None, len(lines)))

    # 初始化结果字典
    result = {}
    current_part = '文章上部分'
    start_idx = 0

    # 根据行号区间提取内容
    for part_name, end_idx in part_boundaries:
        if start_idx < end_idx:
            content_start = start_idx
            if current_part != '文章上部分' and start_idx < len(lines):
                content_start += 1  # 跳过非“文章上部分”的标题行
            content = '\n'.join(lines[content_start:end_idx]).rstrip('\n')
            if content:  # 仅当内容非空时添加到结果
                result[current_part] = content
        # 更新当前部分和起始行号
        current_part = part_name if part_name else current_part
        start_idx = end_idx

    # 如果“文章上部分”为空，移除它
    if '文章上部分' in result and not result['文章上部分']:
        del result['文章上部分']
    return result


def process_third_part(text: str) -> List[Dict[str, Any]]:
    """
    处理第三部分文本，按表分割并提取内容。
    - 按“表X：”或“附注项目：”分割，保留第一个元素作为内容。
    - 如果有“附注项目：”，将其视为独立表，第一个元素作为“表一”内容。
    - 如果没有“表X：”或“附注项目：”，将整个文本作为“表一”。
    - “》：”不用于分割，仅用于重置表编号。
    - 为每个表的内容块添加前置换行符。
    - 移除“特定项目注解”及之后内容。
    """
    # 清理空行和多余空格
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    if not lines:
        return []
    cleaned_text = '\n'.join(lines).replace('''产生的支出。
附注项目：''', '''产生的支出。附注项目''')
    # 移除特定项目注解及之后内容
    intra_pattern = r'\n*特定项目注解'
    intra_match = re.search(intra_pattern, cleaned_text, re.DOTALL)
    if intra_match:
        cleaned_text = cleaned_text[:intra_match.start()].strip()

    # 如果清理后文本为空，返回空列表
    if not cleaned_text:
        return []

    # 定义分割模式，匹配“表X：”或“附注项目：”
    table_pattern = r'(表[一二三四五六]：|附注项目：)'
    parts = re.split(table_pattern, cleaned_text, flags=re.MULTILINE)

    # 过滤空段落
    parts = [part.strip() for part in parts if part.strip()]

    # 检查是否存在分隔符
    has_table = any(re.match(r'表[一二三四五六]：', part) for part in parts)
    has_footnote = '附注项目：' in cleaned_text
    has_special_separator = '》：\n' in cleaned_text

    # 如果没有分隔符，将整个文本作为表一
    if not has_table and not has_footnote:
        return [process_table_content("表一：", "\n" + cleaned_text)]

    result = []
    current_table = "表一："
    current_content = []
    table_counter = 1

    # 如果有“》：”，重置表编号
    if has_special_separator:
        table_counter = 1
        current_table = f"表{num_to_chinese(table_counter)}："

    for i, segment in enumerate(parts):
        # 如果是分隔符
        if re.match(r'表[一二三四五六]：|附注项目：', segment):
            # 保存上一个表的内容
            if current_content:
                result.append(process_table_content(current_table, "\n" + "\n".join(current_content).strip()))
            current_table = segment
            current_content = []
            table_counter += 1
        else:
            # 累积内容，包括第一个元素
            current_content.append(segment)

    # 处理最后一个表的内容
    if current_content:
        result.append(process_table_content(current_table, "\n" + "\n".join(current_content).strip()))

    return result


def num_to_chinese(num: int) -> str:
    """将数字转换为中文表编号（一、二、三等）"""
    chinese_nums = {1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六'}
    return chinese_nums.get(num, '一')


def process_table_content(table_name: str, content: str) -> Dict[str, Any]:
    """
    处理单个表的内容，提取具体说明、子项目列表和参考码表。
    项目名称：从“：”到前一个换行符之间的内容。
    项目描述：从“：”之后到下一个项目名称之前的全部内容（可为空）。
    当子项目名称包含“ - ”或“-”时，拆分为多个子项目。
    subitem_id 移除尾部的 . 和 ．
    当项目id为空且格式无效时，将该子项目信息合并到上一个子项目的描述中。
    优化：当描述前30个字符包含']'时，将']'前的内容拼接到项目名称。
    """
    # print(content)
    content = content.replace(']', ']：').replace(']：至', ']至').replace(']：：', ']：').replace('其中：', '其中:'). \
        replace('第三部分：具体说明及核对关系', '').replace('第Ⅶ部分：贷款投向分行业情况表', '').replace('其中，', ''). \
        replace('．', '.').strip()
    subitem_list = []

    # 使用 re.split 分割内容，匹配以换行或开头开始的“：”行
    subitem_pattern = re.compile(r'^(.*?[.].*?：)', flags=re.MULTILINE)
    split_result = subitem_pattern.split(content)
    # print(split_result)
    # 提取具体说明（第一个子项目前的部分）
    detail_content = split_result[0].rstrip('\n具体说明：').strip() if split_result else content.strip()

    # 处理每个子项目（从索引 1 开始，每隔 2 个取项目名称）
    for i in range(1, len(split_result), 2):  # 跳过捕获组1（完整匹配），取捕获组2（项目名称）
        # print(i)
        raw_name = split_result[i].rstrip('：').strip()
        description = split_result[i + 1].strip() if i + 1 < len(split_result) else ""
        if not raw_name:
            continue

        # 检查是否为多子项目
        has_multiple_sites = ' - ' in raw_name or '-' in raw_name
        has_more_pros = ']至[' in raw_name
        if description.startswith('/'):
            if '：：' in description:
                raw_name = raw_name + description.split('：：')[0].replace('：', "")
                description = description.split('：：')[1]
            elif '：' in description:
                raw_name = raw_name + description.split('：')[0].replace('：', "")
                description = description.split('：')[1]
            else:
                raw_name = raw_name + description.split('/')[0]
                description = description.split('/')
        # 检查是否是两个项目堆积
        has_accumulate_sites = '/' in raw_name
        if has_accumulate_sites:
            # 去除 [] 并分割路径
            cleaned = raw_name.lstrip('[').rstrip(']')
            if not cleaned:
                raise ValueError("raw_name 格式不正确，去除括号后为空")

            raw_ids = cleaned.split('/')
            for raw_id_text in raw_ids:
                ids = []
                raw_id_text = raw_id_text.lstrip('[').rstrip(']')
                if not raw_id_text:
                    raise ValueError("raw_id 为空，无法处理")

                # 正则提取 ID + 名称
                match_last_id = re.match(
                    r'^([^\u4e00-\u9fa5]*?)([\u4e00-\u9fa5].*)$',
                    raw_id_text.strip(),
                    re.DOTALL
                )

                if match_last_id not in ['', None]:
                    # 更新 raw_ids[-1]，去掉中文名及标点
                    id_prefix = match_last_id.group(1).rstrip('.．').strip().rstrip(']')
                    ids.append(id_prefix)

                    # 获取最终中文名
                    raw_name = match_last_id.group(2).strip()
                    # 添加到列表
                    for raw_id in ids:
                        subitem_list.append({
                            "项目id": raw_id,
                            "项目名称": raw_name,
                            "描述": description,  # 确保 description 已定义
                            "参考码表": "",
                            "多子项目": False
                        })

        if has_multiple_sites or has_more_pros:
            query = raw_name + '\n' + description
            site_query = _get_all_site_prompt.replace('{subitem_input}', query)
            prompt_messages = [
                PromptMessage(role="user", content=site_query)
            ]
            qwen_llm_config["prompt_messages"] = prompt_messages

            llm_output = llm_interface.invoke(
                **qwen_llm_config
            )
            llm_output = extract_json_from_response(llm_output.message.content)
            subitem_list.append({
                "项目id": None,
                "项目名称": "",
                "描述": description,
                "参考码表": llm_output['影响法规编号'],
                "多子项目": True,
                "子项目列表": {"起始id": llm_output['影响编号起始id'], "结束id": llm_output['影响编号结束id']}
            })
        else:
            # 解析项目ID和名称：项目id是直到第一个中文字符前的所有字符
            raw_subitem = raw_name.lstrip('[').rstrip(']')
            if raw_subitem.startswith('其中'):
                raw_subitem = raw_subitem[2:].lstrip('\n').strip()
            match_id = re.match(r'^([^\u4e00-\u9fa5]*?)([\u4e00-\u9fa5].*)$', raw_subitem, re.DOTALL)

            if match_id:
                site_id = match_id.group(1).rstrip('.').rstrip('．').strip().rstrip(']').lstrip(':')
                site_name = match_id.group(2).strip()
            else:
                site_id = raw_subitem.rstrip('.').rstrip('．').strip().rstrip(']').lstrip(':')
                site_name = ""

            # 优化处理：检查描述前30个字符是否包含']'
            if description[:30].find(']') != -1:
                # 提取']'前的部分并拼接到项目名称
                split_idx = description.find(']') + 1
                name_append = description[:split_idx].strip().rstrip(']')
                site_name = f"{site_name}{name_append}".strip()
                # 更新描述，移除被拼接的部分
                description = description[split_idx:].strip().lstrip('：')

            # 检查项目ID是否为空且格式无效
            if not site_id and subitem_list and not raw_name.startswith('['):
                # 仅当raw_name不以'['开头（即非标准子项目格式）时合并
                last_item = subitem_list[-1]
                last_item["描述"] = f"{last_item['描述']}\n{raw_name}：{description}".strip()
                continue

            subitem_list.append({
                "项目id": site_id,
                "项目名称": site_name,
                "描述": description.lstrip('：'),
                "参考码表": "",
                "多子项目": False,
                "子项目列表": None
            })

    return {
        "表名": table_name,
        "具体说明": detail_content,
        "子项目列表": subitem_list
    }


def upper_part(text: str) -> dict:
    upper_dict = {}
    match = re.match(r'^[^\u4e00-\u9fa5]+', text)
    requirement_doc_name = match.group(0).strip() if match else ''
    requirement_doc_name = requirement_doc_name.replace('《', "")
    upper_dict['表名ID'] = requirement_doc_name.split('（')[0].strip()
    upper_dict['需求文件名称'] = text.split('表》')[0]+'表》'.strip()
    return upper_dict


def process_part_one(text: str) -> str:
    return text


def process_part_two(text: str) -> dict:
    # 定义结果字典
    result = {
        '报表名称': '',
        '报表编码': '',
        '填报机构': '',
        '报送口径、频度及时间': '',
        '报送方式': '',
        '数据单位': '',
        '四舍五入要求': '',
        '填报币种': ''
    }

    # 定义字段与对应的关键词模式（支持多个关键词）
    field_patterns = {
        '报表名称': r'报表名称|报告名称|表名',
        '报表编码': r'报表编码|表名|编码|CODE|Report Code',
        '填报机构': r'填报机构|填报单位|上报机构',
        '报送口径、频度及时间': r'报送口径、频度及时间|报送频率|上报周期',
        '报送方式': r'报送方式|提交方式',
        '数据单位': r'数据单位|计量单位|单位',
        '四舍五入要求': r'四舍五入要求|取整规则',
        '填报币种': r'填报币种|货币种类|Currency'
    }

    # 按照字段顺序处理
    fields = list(field_patterns.keys())

    for i in range(len(fields)):
        current_field = fields[i]
        pattern = field_patterns[current_field]

        # 构建正则表达式：匹配任意一种关键词 + “：” 或 “:”
        regex = rf'({pattern})[：:]'
        match = re.search(regex, text)

        if not match:
            continue  # 如果没找到当前字段的起始标记，跳过

        start_index = match.end()

        # 查找下一个字段的起始位置或字符串末尾
        next_start_index = len(text)
        for j in range(i + 1, len(fields)):
            next_pattern = field_patterns[fields[j]]
            next_match = re.search(rf'({next_pattern})[：:]', text[start_index:])
            if next_match:
                next_start_index = start_index + next_match.start()
                break
            else:
                continue

        # 提取值
        value = text[start_index:next_start_index].strip()
        # 移除非中文字符结尾
        value = re.sub(r'[^\u4e00-\u9fff]+$', '', value).strip()
        result[current_field] = value.rstrip('。')

    return result


def process_part_four(text: str) -> List[Dict[str, str]]:
    """
    处理表间核对逻辑，优先按 '_数字:' 分割表，若无法分割则按 '》：' 分割。
    提取“表间核对关系：”或“跨表校验:”后的内容，按等号左侧的 _N_（N=1到7）分配表间核对逻辑。
    表内核对逻辑除最后一个表外，结束位置提前一行；表间核对逻辑不包含“表间核对关系：”行。

    Args:
        text (str): 输入的文本内容

    Returns:
        List[Dict[str, str]]: 包含表名、表内核对逻辑和表间核对逻辑的字典列表
    """
    text = text.replace('（全部采用权重法的填报机构适用）', '')
    if not text.strip():
        return []

    # 1. 提取表间核对逻辑及其起始位置
    inter_table_logic = ""
    inter_start_pos = -1
    inter_pattern = r'\n*(?:表间核对关系：|跨表校验:)\s*(.*?)(?=_\d+:|》：|\Z)'
    inter_match = re.search(inter_pattern, text, flags=re.DOTALL)
    if inter_match:
        inter_start_pos = inter_match.end()
        inter_table_logic = inter_match.group(1).strip()

    def extract_table_number(text):
        """
        从文本中提取表编号，返回阿拉伯数字（1~7）
        支持格式：
            - 表[一二三四五六七]
            - _I_, _II_, ..., _VII_
        """
        # 匹配中文数字：表一、表二...
        chinese_match = re.search(r'表([一-七])', text)
        if chinese_match:
            ch_to_num = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7}
            return chinese_match.group(1), ch_to_num.get(chinese_match.group(1))

        # 匹配罗马数字：_I_, _II_, ..., _VII_
        roman_match = re.search(r'_(I{1,3}|IV|V|VI?I{0,2})_', text)
        if roman_match:
            roman_num = roman_match.group(1)
            roman_map = {
                'I': 1, 'II': 2, 'III': 3, 'IV': 4,
                'V': 5, 'VI': 6, 'VII': 7
            }
            return roman_num, roman_map.get(roman_num)

        # 默认返回 None 或 fallback 到表1
        return None, 1

    # 主逻辑
    inter_logic_by_table = {f"表{i}": [] for i in range(1, 8)}  # 初始化 表1 到 表7

    if inter_table_logic:
        logic_lines = inter_table_logic.strip().split('\n')
        for line in logic_lines:
            line = line.strip()
            if not line:
                continue

            left_side = line.split('=', 1)[0]  # 只取等号前部分
            _, table_number = extract_table_number(left_side)

            table_key = f"表{table_number}"
            if table_key in inter_logic_by_table:
                inter_logic_by_table[table_key].append(line)

    # 3. 尝试按 '_数字:' 分割
    table_pattern = r'_([1-7]):|表([一二三四五六])：'
    table_matches = list(re.finditer(table_pattern, text))
    result = []

    if table_matches:
        # 按 '_数字:' 分割
        for i, match in enumerate(table_matches):
            table_num = match.group(1) if match.group(1) else i + 1
            start_pos = match.end()
            # 结束位置处理
            if i + 1 < len(table_matches):
                end_pos = table_matches[i + 1].start()
                content_before = text[start_pos:end_pos]
                last_newline = content_before.rfind('\n')
                if last_newline != -1:
                    end_pos = start_pos + last_newline
            else:
                end_pos = inter_start_pos if inter_start_pos != -1 else len(text)

            content = text[start_pos:end_pos].strip()
            # 移除“表内核对关系：”及其前面的内容
            intra_pattern = r'.*\n*表内核对关系：\s*(.*?)(?=\n_|\Z)'
            intra_match = re.search(intra_pattern, content, re.DOTALL)
            intra_content = intra_match.group(1).strip() if intra_match else content

            table_info = {
                "表名": f"表{table_num}",
                "表内核对逻辑": intra_content,
                "表间核对逻辑": '\n'.join(inter_logic_by_table[f"表{table_num}"])
            }
            result.append(table_info)
    else:
        # 尝试按 '》：' 分割
        section_pattern = r'》：'
        section_matches = list(re.finditer(section_pattern, text))
        if section_matches:
            for i, match in enumerate(section_matches):
                table_num = str(i + 1)
                start_pos = match.end()
                if i + 1 < len(section_matches):
                    end_pos = section_matches[i + 1].start()
                    content_before = text[start_pos:end_pos]
                    last_newline = content_before.rfind('\n')
                    if last_newline != -1:
                        end_pos = start_pos + last_newline
                else:
                    end_pos = inter_start_pos if inter_start_pos != -1 else len(text)

                content = text[start_pos:end_pos].strip()
                intra_pattern = r'.*\n*表内核对关系：\s*(.*?)(?=\n》：|\Z)'
                intra_match = re.search(intra_pattern, content, re.DOTALL)
                intra_content = intra_match.group(1).strip() if intra_match else content

                table_info = {
                    "表名": f"表{table_num}",
                    "表内核对逻辑": intra_content,
                    "表间核对逻辑": '\n'.join(
                        inter_logic_by_table[f"表{table_num}"]) if f"表{table_num}" in inter_logic_by_table else ""
                }
                result.append(table_info)
        else:
            # 单表情况
            content = text[:inter_start_pos].strip() if inter_start_pos != -1 else text.strip()
            intra_pattern = r'.*\n*表内核对关系：\s*(.*?)(?=\n_|\Z)'
            intra_match = re.search(intra_pattern, content, re.DOTALL)
            intra_content = intra_match.group(1).strip() if intra_match else content

            table_info = {
                "表名": "表1",
                "表内核对逻辑": intra_content,
                "表间核对逻辑": '\n'.join(inter_logic_by_table["表1"])
            }
            result.append(table_info)

    return result


def process_part_five(text: str) -> str:
    return text


def process_part_six(text: str) -> str:
    return text


def process_doc_file(file_path: str, output_dir: str = "/data/ideal/code/hr_code/test_dd_file/transform_dir") -> Dict[
    str, Any]:
    """
    转换 .doc 到 .docx，解析内容，并返回文件源数据信息。

    Args:
        file_path (str): .doc 文件路径
        output_dir (str): 输出目录

    Returns:
        Dict[str, Any]: 包含解析部分和源数据信息的字典
            - 各部分（如 "第一部分", "第三部分"）: 解析后的内容
            - "source_info": 源数据信息，包括文件名、docx路径和作者
    """
    # 转换 .doc 到 .docx
    docx_path = convert_doc_to_docx(file_path, output_dir)

    # 提取并解析文本
    text = accept_all_revisions(docx_path)
    parts = split_by_parts(text)
    if parts == {}:
        return {
            "文章上部分": {"表名ID": "", "需求文件名称": ""},
            "第一部分": {},
            "第二部分": {"数据单位": "", "四舍五入要求": "", "填报币种": "", "报表名称": ""},
            "第三部分": [{"子项目列表": [], "具体说明": ""}],
            "第四部分": [{'表内核对逻辑': '', '表间核对逻辑': ''}]
        }

    result = {}
    for part_name, part_text in parts.items():
        if part_name == "第三部分":
            result[part_name] = process_third_part(part_text)
        elif part_name == "第一部分":
            result[part_name] = process_part_one(part_text)
        elif part_name == "第二部分":
            result[part_name] = process_part_two(part_text)
        elif part_name == "第四部分":
            result[part_name] = process_part_four(part_text)
        elif part_name == "第五部分":
            result[part_name] = process_part_five(part_text)
        elif part_name == "第六部分":
            result[part_name] = process_part_six(part_text)
        elif part_name == "文章上部分":
            result[part_name] = upper_part(part_text)

    # 提取源数据信息
    original_filename = os.path.splitext(os.path.basename(file_path))[0]
    docx_filename = os.path.basename(docx_path)

    # 提取作者信息
    try:
        doc = Document(docx_path)
        author = doc.core_properties.author or ""
    except Exception:
        author = ""

    result["source_info"] = {
        "original_filename": original_filename,
        "docx_path": docx_path,
        "docx_filename": docx_filename,
        "author": author
    }

    return result


# 判断是不是纯数字组成
def is_pure_number(s):
    return re.fullmatch(r'(\d+\.)*\d+', s) is not None


def fill_project_descriptions(project_names, project_details):
    """
    根据项目名称匹配并填充描述信息。

    Args:
        project_names (list): 包含项目的列表，每个项目是一个字典，至少包含 "name" 和 "desc" 键。
        project_details (list): 包含详细项目信息的列表，每个项目是一个字典，至少包含 "项目名称" 和 "描述" 键。

    Returns:
        list: 更新后的 project_names 列表。
    """
    # 构建有序指标列表
    unique_indicator_list = sorted(set(proj["y1"] for proj in project_names if proj.get("y1")),
                                   key=lambda x: (len(x), x))

    # 构建有序维度列表
    def sort_by_hierarchy(data):
        def hierarchical_key(s):
            parts = re.split(r"(\d+)", s)
            return [int(p) if p.isdigit() else p.lower() for p in parts if p]

        pure_items = sorted([item for item in data if is_pure_number(item)], key=hierarchical_key)
        mixed_items = sorted([item for item in data if not is_pure_number(item)], key=hierarchical_key)
        return pure_items + mixed_items

    unique_dimension_list = sort_by_hierarchy(set(proj["name_id"] for proj in project_names if proj.get("name_id")))

    # 初始化映射
    project_mappings = {
        "dimension": {proj["name_id"]: proj["project_name"] for proj in project_names if
                      proj.get("name_id") and proj.get("project_name")},
        "indicator": dict(sorted(
            {proj["y1"]: proj["y2"].replace('\n', '') for proj in project_names if
             proj.get("y1") and proj.get("y2")}.items(),
            key=lambda item: (len(item[0]), item[0])
        ))
    }

    # 处理多子项目
    check_code_desc = [
        {k: detail.get(k, detail.get("子项目列表", {}).get(k)) for k in ["起始id", "结束id", "参考码表", "描述"]}
        for detail in project_details if detail.get("多子项目")
    ]

    def update_range(items_list, start_id, end_id, desc, check_code, is_indicator=False):
        if not (start_id and end_id) or start_id not in items_list or end_id not in items_list:
            return
        start_index = items_list.index(start_id)
        end_index = items_list.index(end_id) + 1
        for key in items_list[start_index:end_index]:
            if is_indicator:
                project_mappings["indicator"][key] = desc
            else:
                project_details.append({
                    "项目id": key,
                    "项目名称": project_mappings["dimension"].get(key, ""),
                    "描述": desc or project_mappings["dimension"].get(key, ""),
                    "参考码表": check_code
                })

    for de in check_code_desc:
        start_id, end_id, desc, check_code = de["起始id"], de["结束id"], de["描述"], de["参考码表"]
        update_range(unique_indicator_list, start_id, end_id, desc, check_code, is_indicator=True)
        update_range(unique_dimension_list, start_id, end_id, desc, check_code)

    # 构建映射
    mappings = {
        "id_to_desc": {},
        "name_to_desc": {},
        "id_to_code": {},
        "name_to_code": {}
    }
    # print(project_details)
    for detail in project_details:
        project_id = detail.get("项目id")
        if "项目名称" in detail and "描述" in detail and detail["项目名称"]:
            mappings["name_to_desc"][detail["项目名称"].strip()] = detail["描述"]
        if project_id and "描述" in detail:
            mappings["id_to_desc"][project_id.replace("．", ".") if isinstance(project_id, str) else project_id] = \
                detail["描述"]
        if project_id and "参考码表" in detail:
            mappings["id_to_code"][project_id.replace("．", ".") if isinstance(project_id, str) else project_id] = \
                detail["参考码表"]
        if "项目名称" in detail and "参考码表" in detail and detail["项目名称"]:
            mappings["name_to_code"][detail["项目名称"].strip()] = detail["参考码表"]
    # print(mappings["name_to_desc"])
    # 填充描述
    for project in project_names:
        name_id = project.get("name_id")
        name = project.get("project_name").strip()

        if name and name in mappings["name_to_desc"]:
            project["dimension_desc"] = mappings["name_to_desc"][name]
            project["check_code_desc"] = mappings["name_to_code"].get(name, "")
        elif name_id and name_id in mappings["id_to_desc"]:
            project["dimension_desc"] = mappings["id_to_desc"][name_id]
            project["check_code_desc"] = mappings["id_to_code"].get(name_id, "")
        else:
            project["dimension_desc"] = ""
            project["check_code_desc"] = ""
        # 指标的内容填充
        if project["y1"] and project["y1"] in mappings["id_to_desc"]:
            project["indicator_desc"] = mappings["id_to_desc"][project["y1"]]
        elif project["y2"].replace('\n', '') and project["y2"].replace('\n', '') in mappings["name_to_desc"]:
            project["indicator_desc"] = mappings["name_to_desc"][project["y2"].replace('\n', '')]
        else:
            project["indicator_desc"] = project_mappings["indicator"].get(project["y1"], "")
    return project_names


def apply_llm_output_to_project(llm_output: List[Dict[str, Any]], project_data: List[Dict[str, Any]]) -> List[
    Dict[str, Any]]:
    """
    将 LLM 输出结果应用到 project_data 上，根据匹配规则填充 check_desc 字段。
    支持同一个项目匹配多个 check_desc，用换行符分隔。
    当 is_all_project 为 True 时，规则应用于所有项目，但排除 remove_project_ids 中的 name_id。

    Args:
        llm_output (List[Dict[str, Any]]): LLM 返回的数据列表，每项包含 project_id、val、type、is_all_project、remove_project_ids 等字段。
        project_data (List[Dict[str, Any]]): 原始项目数据列表，每项包含 name_id、y1、name、desc 等字段。

    Returns:
        List[Dict[str, Any]]: 更新后的 project_data，保留所有原始条目。
    """
    # logger = logging.getLogger(__name__)

    for output in llm_output:
        if output is None:
            # logger.warning("Skipping None output in llm_output")
            continue

        project_id = output.get('project_id')
        check_desc = output.get('val')
        if not isinstance(check_desc, str) or not check_desc:
            # logger.warning(f"Skipping output with invalid check_desc: {check_desc}")
            continue

        llm_type = output.get('type', '').lower()
        is_all_project = output.get('is_all_project', False)
        if isinstance(is_all_project, str):
            is_all_project = is_all_project.lower() == 'true'

        remove_project_ids_set = {str(pid) for pid in output.get('remove_project_ids', [])}

        if not is_all_project and project_id is None:
            # logger.warning("Skipping output with missing project_id when is_all_project is False")
            continue

        for project in project_data:
            if 'name_id' not in project:
                # logger.warning(f"Skipping project without name_id: {project}")
                continue

            name_id_str = str(project['name_id'])
            project_y1 = project.get('y1', '').lower()

            # 检查类型匹配
            if llm_type != 'all' and project_y1 != llm_type:
                continue

            # 检查项目匹配
            if is_all_project:
                if name_id_str in remove_project_ids_set:
                    # logger.debug(f"Skipping project {name_id_str} as it is in remove_project_ids")
                    continue
            else:
                if name_id_str != str(project_id):
                    # logger.debug(f"Skipping project {name_id_str} as it does not match project_id {project_id}")
                    continue

            # 更新 check_desc
            if 'check_desc' in project:
                project['check_desc'] += '\n' + check_desc
            else:
                project['check_desc'] = check_desc

    return project_data


def process_non_chinese_line(line):
    """
    处理非中文行，提取所有方括号内的内容并生成多个结果字典。

    Args:
        line: 输入的字符串行

    Returns:
        list: 包含解析后信息的字典列表，或空列表（如果没有匹配到有效的模式）
    """
    # 匹配所有 [xxx] 结构的内容
    # print(line)
    bracket_matches = re.findall(r'\[([^\]]+)\]', line)
    if not bracket_matches:
        return []

    results = []

    # 处理每个方括号内的内容
    for prefix in bracket_matches:
        # 检查前缀末尾是否有 A/B/C/D/E/R
        col_match = re.search(r'([A-Za-z])$', prefix)
        col_type = col_match.group(1) if col_match else "all"

        # 去除类型字符（如果有）和末尾的点
        if col_type != "all":
            base_id = prefix[:-1].rstrip('.')
        else:
            base_id = prefix.rstrip('.')

        # 判断是否为所有项目
        is_all_project = not base_id

        # 创建结果字典
        result = {
            "type": col_type,
            "val": line.strip('；').strip(),  # 去除行尾分号和前后空白
            "project_id": base_id,
            "is_all_project": is_all_project,
            "remove_project_ids": []
        }
        results.append(result)
        # print(results)
    return results if results else []


def contains_chinese(text):
    """判断字符串中是否包含中文字符"""
    return bool(re.search(r'[\u4e00-\u9fa5]', text))


def apply_styles_from_json(ws, styles_data, max_rows=5):
    """
    将从 JSON 加载的样式信息应用到指定 worksheet 的前几行
    """
    for row_idx in range(1, min(max_rows + 1, len(styles_data["rows"]) + 1)):
        row_data = styles_data["rows"][row_idx - 1]
        for col_idx, cell_style in enumerate(row_data, start=1):
            cell = ws.cell(row=row_idx, column=col_idx)

            # 设置字体
            font_info = cell_style.get("font", {})
            cell.font = Font(
                name=font_info.get("name", "Calibri"),
                size=int(font_info.get("size", 11)),
                bold=font_info.get("bold", False),
                italic=font_info.get("italic", False),
                color=font_info.get("color", "FF000000")
            )

            # 设置背景色
            fill_info = cell_style.get("fill", {})
            bg_color = fill_info.get("bgColor", "FFFFFFFF")
            cell.fill = PatternFill(patternType="solid", fgColor=bg_color)

            # 设置对齐
            align_info = cell_style.get("alignment", {})
            h_align = align_info.get("horizontal", "left")
            v_align = align_info.get("vertical", "top")
            wrap_text = align_info.get("wrap_text", False)
            cell.alignment = Alignment(horizontal=h_align, vertical=v_align, wrap_text=wrap_text)


async def split_query_by_newlines(query, chunk_size=5):
    lines = query.split('\n')
    return ['\n'.join(lines[i:i + chunk_size]) for i in range(0, len(lines), chunk_size)]


async def process_chunk(chunk, valid_ids, valid_column):
    llm_query = _project_prompt.replace('{project_data}', chunk).replace('{project_ids}',
                                                                         ','.join(valid_ids)[:700]).replace(
        '{valid_column}', ','.join(list(valid_column))[:300])
    prompt_messages = [PromptMessage(role="user", content=llm_query)]
    qwen_llm_config["prompt_messages"] = prompt_messages
    llm_output = await llm_interface.ainvoke(**qwen_llm_config)
    return extract_json_from_response(llm_output.message.content)


async def process_llm_queries(check_project_data, valid_ids, valid_column):
    llm_output = []
    if check_project_data:
        query_chunks = await split_query_by_newlines(check_project_data)
        tasks = [process_chunk(chunk, valid_ids, valid_column) for chunk in query_chunks]

        # 使用 return_exceptions=True 来捕获异常，而不是中断整个 gather
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 遍历结果，过滤掉异常对象
        for result in results:
            if isinstance(result, Exception):
                # 可选：记录错误信息
                print(f"处理 chunk 时发生错误: {result}")
                continue
            # 假设正常结果是一个列表
            llm_output.extend(result)

    # print('这是大模型的输出', llm_output)
    return llm_output


# 清理 sheet 名称的工具函数
def clean_sheet_name(name: str, index: int) -> str:
    if not name.strip():
        return f"Sheet_{index + 1}"
    name = name.replace(':', '_').replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_')
    return name[:31]


# 用户选定要专属执行的指标
def get_custom_indicator(project_names: dict, sheet_names: list, file_path: str, csv_path: str):
    out_dict = {}
    output_dir = os.path.dirname(file_path)
    data_nums = len(project_names.keys())
    project_names = list(project_names.items())

    filename_without_ext = Path(csv_path).stem

    processed_parts = process_doc_file(file_path, output_dir)
    # print(processed_parts['第三部分'])
    if len(project_names) < data_nums:
        raise ValueError(f"project_names 长度 ({len(project_names)}) 小于 data_nums ({data_nums})")

    if len(processed_parts['第三部分']) == 1 and len(processed_parts['第三部分']) < data_nums:
        processed_parts['第三部分'] = processed_parts['第三部分'] * data_nums
        processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                     in range(data_nums - 1)]
    elif len(processed_parts['第三部分']) < data_nums:
        processed_parts['第三部分'] = processed_parts['第三部分'] + [processed_parts['第三部分'][-1] for _
                                                                     in range(
                data_nums - len(processed_parts['第三部分']))]
        processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                     in range(
                data_nums - len(processed_parts['第四部分']))]
    elif len(processed_parts['第四部分']) != data_nums:
        processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                     in range(data_nums - 1)]

    for i in range(data_nums):
        check_desc = processed_parts['第四部分'][i]['表内核对逻辑']
        project_name = processed_parts['第三部分'][i]['子项目列表']
        valid_projects = [proj for proj in project_names[i][1] if proj['valid'] == 'True']
        project_data = fill_project_descriptions(valid_projects, project_name)

        valid_ids = ["\"" + proj["name_id"] + "\"" for proj in project_data]
        valid_column = set(proj["y1"] for proj in project_data)

        lines = check_desc.split('\n')
        chinese_list = [line.strip() for line in lines if line.strip() and contains_chinese(line)]
        non_chinese_list = [
            subline for line in lines if line.strip() and not contains_chinese(line)
            for subline in line.split('，')
        ]
        desc_check = []

        for no_chinese in non_chinese_list:
            desc_check.extend(process_non_chinese_line(no_chinese))

        check_project_data = '\n'.join(chinese_list) + processed_parts['第四部分'][i]['表间核对逻辑']
        # todo 为了减慢大模型的识别问题，暂时将过大模型部分移除掉，规则校验这样通过逻辑补充
        # llm_output = asyncio.run(
        #     process_llm_queries(check_project_data, valid_ids, valid_column)) if check_project_data else []
        # llm_output.extend(desc_check)
        llm_output = desc_check
        output = apply_llm_output_to_project(llm_output, project_data)

        data_format = '1.数据单位：' + processed_parts['第二部分']['数据单位'] + '\n2.四舍五入要求：' + \
                      processed_parts['第二部分']['四舍五入要求'] + '\n3.填报币种：' + processed_parts['第二部分'][
                          '填报币种']
        table_name = processed_parts['第二部分']['报表名称']
        table_name_id, requirement_doc_name = processed_parts['文章上部分']['表名ID'], \
            processed_parts['文章上部分']['需求文件名称']

        output = [{
            "entry_type": "ITEM",
            "DR01": "ADS",
            "DR02": requirement_doc_name,
            "DR03": "不适用",
            "DR04": "不适用",
            "DR05": "不适用",
            "DR06": sheet_names[i] if '表' in sheet_names[i] else table_name,
            "DR07": upper_part(filename_without_ext)['表名ID'],
            "DR08": "增量",
            "DR09": (item['name_id'] + '.' + item['y1'] + item['project_name'] + '_' + item['y2']).replace(
                '明细表没有维度', '').strip('.').strip('_'),
            "DR10": (item['name_id'] + '.' + item['y1']).strip('.'),
            "DR11": "不适用",
            "DR12": "不适用",
            "DR13": "不适用",
            "DR14": "不适用",
            "DR15": "不适用",
            "DR16": data_format,
            "DR17": '维度描述：' + (
                item["dimension_desc"] if item.get("dimension_desc") not in ('', None)
                else item.get('project_name', '')
            ) + '\n指标描述：' + item.get("indicator_desc", ""),
            "DR18": item.get("check_code_desc") if item.get("check_code_desc") not in [None, ""] else "不适用",
            "DR19": "月报",
            "DR20": item.get("check_desc") if item.get("check_desc") not in [None, ""] else "不适用",
            "DR21": "不脱敏",
            "DR22": "",
            "mark": {
                "markSheet": sheet_names[i] if '_Table<momomo>_' not in sheet_names[i] else
                sheet_names[i].split('_Table<momomo>_')[0],
                "markCellType": "report",
                "markCellPosition": excel_coordinate_to_a1(item.get("original_position", ())),
                "markCellReportRegionX": item.get("original_position", ()).split(',')[0].replace('(', '').strip(),
                "markCellReportRegionY": item.get("original_position", ()).split(',')[1].replace(')', '').strip()}
        } for index, item in enumerate(output)]

        table_name = sheet_names[i]
        out_dict[table_name] = output

    return out_dict


# 为接口定制开发，不需要将实际产出的输出到文件
def extraction_report(file_path: str, csv_dir: str, sheet_names_input: str):
    out_dict = {}
    output_dir = os.path.dirname(file_path)

    filename_without_ext = Path(csv_dir).stem

    tran_type, tran_xls_path = process_excel_file(csv_dir, sheet_names_input)

    project_names, sheet_names = process_excel(tran_xls_path)
    # print(len(sheet_names))
    data_nums = len(project_names.keys())
    project_names = list(project_names.items())

    processed_parts = process_doc_file(file_path, output_dir)
    if len(project_names) < data_nums:
        raise ValueError(f"project_names 长度 ({len(project_names)}) 小于 data_nums ({data_nums})")

    if len(processed_parts['第三部分']) == 1 and len(processed_parts['第三部分']) < data_nums:
        processed_parts['第三部分'] = processed_parts['第三部分'] * data_nums
        processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                     in range(data_nums - 1)]
    elif len(processed_parts['第三部分']) < data_nums:
        processed_parts['第三部分'] = processed_parts['第三部分'] + [processed_parts['第三部分'][-1] for _
                                                                     in range(
                data_nums - len(processed_parts['第三部分']))]
        processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                     in range(
                data_nums - len(processed_parts['第四部分']))]
    elif len(processed_parts['第四部分']) != data_nums:
        processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                     in range(data_nums - 1)]

    for i in range(data_nums):
        check_desc = processed_parts['第四部分'][i]['表内核对逻辑']
        project_name = processed_parts['第三部分'][i]['子项目列表']
        valid_projects = [proj for proj in project_names[i][1] if proj['valid'] == 'True']
        project_data = fill_project_descriptions(valid_projects, project_name)

        valid_ids = ["\"" + proj["name_id"] + "\"" for proj in project_data]
        valid_column = set(proj["y1"] for proj in project_data)

        lines = check_desc.split('\n')
        chinese_list = [line.strip() for line in lines if line.strip() and contains_chinese(line)]
        non_chinese_list = [
            subline for line in lines if line.strip() and not contains_chinese(line)
            for subline in line.split('，')
        ]
        desc_check = []

        for no_chinese in non_chinese_list:
            desc_check.extend(process_non_chinese_line(no_chinese))

        check_project_data = '\n'.join(chinese_list) + processed_parts['第四部分'][i]['表间核对逻辑']
        # todo 为了减慢大模型的识别问题，暂时将过大模型部分移除掉，规则校验这样通过逻辑补充
        # llm_output = asyncio.run(
        #     process_llm_queries(check_project_data, valid_ids, valid_column)) if check_project_data else []
        # llm_output.extend(desc_check)
        llm_output = desc_check
        output = apply_llm_output_to_project(llm_output, project_data)

        data_format = '1.数据单位：' + processed_parts['第二部分']['数据单位'] + '\n2.四舍五入要求：' + \
                      processed_parts['第二部分']['四舍五入要求'] + '\n3.填报币种：' + processed_parts['第二部分'][
                          '填报币种']
        table_name = processed_parts['第二部分']['报表名称']
        table_name_id, requirement_doc_name = processed_parts['文章上部分']['表名ID'], \
            processed_parts['文章上部分']['需求文件名称']

        output = [{
            "entry_type": "ITEM",
            "DR01": "ADS",
            "DR02": requirement_doc_name,
            "DR03": "不适用",
            "DR04": "不适用",
            "DR05": "不适用",
            "DR06": sheet_names[i] if '表' in sheet_names[i] else table_name,
            "DR07": upper_part(filename_without_ext)['表名ID'],
            "DR08": "增量",
            "DR09": (item['name_id'] + '.' + item['y1'] + item['project_name'] + '_' + item['y2']).replace(
                '明细表没有维度', '').strip('.').strip('_'),
            "DR10": (item['name_id'] + '.' + item['y1']).strip('.'),
            "DR11": "不适用",
            "DR12": "不适用",
            "DR13": "不适用",
            "DR14": "不适用",
            "DR15": "不适用",
            "DR16": data_format,
            "DR17": '维度描述：' + (
                item["dimension_desc"] if item.get("dimension_desc") not in ('', None)
                else item.get('project_name', '')
            ) + '\n指标描述：' + item.get("indicator_desc", ""),
            "DR18": item.get("check_code_desc") if item.get("check_code_desc") not in [None, ""] else "不适用",
            "DR19": "月报",
            "DR20": item.get("check_desc") if item.get("check_desc") not in [None, ""] else "不适用",
            "DR21": "不脱敏",
            "DR22": "",
            "mark": {
                "markSheet": sheet_names[i] if '_Table<momomo>_' not in sheet_names[i] else
                sheet_names[i].split('_Table<momomo>_')[0],
                "markCellType": "report",
                "markCellPosition": excel_coordinate_to_a1(item.get("original_position", ())),
                "markCellReportRegionX":"",
                "markCellReportRegionY":""}
                # "markCellReportRegionX": item.get("original_position", ()).split(',')[0].replace('(', '').strip(),
                # "markCellReportRegionY": item.get("original_position", ()).split(',')[1].replace(')', '').strip()}
        } for index, item in enumerate(output)]

        if i == 0:
            # 在列表开头插入TABLE
            if '_Table<momomo>_' not in sheet_names[i]:
                output.insert(0, {
                    "entry_type": "TABLE",
                    "DR01": "范围",
                    "DR02": requirement_doc_name,
                    "DR03": "不适用",
                    "DR04": "不适用",
                    "DR05": "不适用",
                    "DR06": sheet_names[i] if '表' in sheet_names[i] else table_name,
                    "DR07": upper_part(filename_without_ext)['表名ID'],
                    "DR08": "不适用",
                    "DR09": "报送范围",
                    "DR10": "不适用",
                    "DR11": "不适用",
                    "DR12": "不适用",
                    "DR13": "不适用",
                    "DR14": "不适用",
                    "DR15": "不适用",
                    "DR16": "不适用",
                    "DR17": processed_parts['第三部分'][i]['具体说明'],
                    "DR18": "不适用",
                    "DR19": "月报",
                    "DR20": "不适用",
                    "DR21": "不脱敏",
                    "DR22": "",
                    "mark": {
                        "markSheet": sheet_names[i],
                        "markCellType": "report",
                        "markCellPosition": "",
                        "markCellReportRegionX": "",
                        "markCellReportRegionY": ""
                    }
                })

        table_name = sheet_names[i]
        out_dict[table_name] = output

    return out_dict


def execute_processing_logic(file_path: str, csv_dir: str, sheet_names_input: str):
    out_dict = {}
    output_dir = os.path.dirname(file_path)

    try:
        # 加载样式 JSON
        json_path = style_json_path
        if not os.path.exists(json_path):
            raise FileNotFoundError(f"未找到样式文件: {json_path}")
        try:
            with open(json_path, "r", encoding="utf-8") as f:
                style_data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"样式文件 {json_path} 中的 JSON 格式无效: {str(e)}")
        tran_type, tran_xls_path = process_excel_file(csv_dir, sheet_names_input)
        project_names, sheet_names = process_excel(tran_xls_path)
        data_nums = len(project_names.keys())
        project_names = list(project_names.items())

        # print(project_names)
        processed_parts = process_doc_file(file_path, output_dir)
        # print(processed_parts)
        if len(project_names) < data_nums:
            raise ValueError(f"project_names 长度 ({len(project_names)}) 小于 data_nums ({data_nums})")
        # print(processed_parts)
        if len(processed_parts['第三部分']) == 1 and len(processed_parts['第三部分']) < data_nums:
            processed_parts['第三部分'] = processed_parts['第三部分'] * data_nums
            processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                         in range(data_nums - 1)]
        elif len(processed_parts['第三部分']) < data_nums:
            processed_parts['第三部分'] = processed_parts['第三部分'] + [processed_parts['第三部分'][-1] for _
                                                                         in range(
                    data_nums - len(processed_parts['第三部分']))]
            processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                         in range(
                    data_nums - len(processed_parts['第四部分']))]
        elif len(processed_parts['第四部分']) != data_nums:
            processed_parts['第四部分'] = processed_parts['第四部分'] + [{'表内核对逻辑': '', '表间核对逻辑': ''} for _
                                                                         in range(data_nums - 1)]
        # Excel 写入
        excel_path = os.path.join(output_dir,
                                  f"{processed_parts['source_info']['docx_filename']}_processed_output.xlsx")
        for i in range(data_nums):
            check_desc = processed_parts['第四部分'][i]['表内核对逻辑']
            project_name = processed_parts['第三部分'][i]['子项目列表']
            # print(project_names)
            valid_projects = [proj for proj in project_names[i][1] if proj['valid'] == 'True']
            project_data = fill_project_descriptions(valid_projects, project_name)

            valid_ids = ["\"" + proj["name_id"] + "\"" for proj in project_data]
            valid_column = set(proj["y1"] for proj in project_data)

            lines = check_desc.split('\n')
            chinese_list = [line.strip() for line in lines if line.strip() and contains_chinese(line)]
            non_chinese_list = [
                subline for line in lines if line.strip() and not contains_chinese(line)
                for subline in line.split('，')
            ]
            desc_check = []

            for no_chinese in non_chinese_list:
                desc_check.extend(process_non_chinese_line(no_chinese))
            # print('这是非中文给出的', desc_check)
            check_project_data = '\n'.join(chinese_list) + processed_parts['第四部分'][i]['表间核对逻辑']
            # todo 为了减慢大模型的识别问题，暂时将过大模型部分移除掉，规则校验这样通过逻辑补充
            # llm_output = asyncio.run(
            #     process_llm_queries(check_project_data, valid_ids, valid_column)) if check_project_data else []
            # llm_output.extend(desc_check)
            llm_output = desc_check
            output = apply_llm_output_to_project(llm_output, project_data)
            # print(output)
            # 构建格式要求和表名部分（保持不变）
            data_format = '1.数据单位：' + processed_parts['第二部分']['数据单位'] + '\n2.四舍五入要求：' + \
                          processed_parts['第二部分']['四舍五入要求'] + '\n3.填报币种：' + processed_parts['第二部分'][
                              '填报币种']
            table_name = processed_parts['第二部分']['报表名称']
            table_name_id, requirement_doc_name = processed_parts['文章上部分']['表名ID'], \
                processed_parts['文章上部分']['需求文件名称']

            # 构建输出（保持不变）
            output = [{
                "DR01": "ADS",
                "DR02": requirement_doc_name,
                "DR03": "不适用",
                "DR04": "不适用",
                "DR05": "不适用",
                "DR06": sheet_names[i] if '表' in sheet_names[i] else table_name,
                "DR07": upper_part(sheet_names[i] if '表' in sheet_names[i] else table_name)['表名ID'] if
                upper_part(sheet_names[i] if '表' in sheet_names[i] else table_name)['表名ID'] not in ['', None]
                else upper_part(requirement_doc_name)['表名ID'],
                "DR08": "不适用",
                "DR09": (item['name_id'] + '.' + item['y1'] + item['project_name'] + '_' + item['y2']).replace(
                    '明细表没有维度', '').strip('.').strip('_'),
                "DR10": (item['name_id'] + '.' + item['y1']).strip('.'),
                "DR11": "不适用",
                "DR12": "不适用",
                "DR13": "不适用",
                "DR14": "不适用",
                "DR15": "不适用",
                "DR16": data_format,
                "DR17": '维度描述：' + (
                    item["dimension_desc"] if item.get("dimension_desc") not in ('', None)
                    else item.get('project_name', '')
                ) + '\n指标描述：' + item.get("indicator_desc", ""),
                "DR18": item.get("check_code_desc") if item.get("check_code_desc") not in [None, ""] else "不适用",
                "DR19": "月报",
                "DR20": item.get("check_desc") if item.get("check_desc") not in [None, ""] else "不适用",
                "DR21": "不脱敏",
                "DR22": ""
            } for item in output]
            output.insert(0, {
                "DR01": "范围",
                "DR02": requirement_doc_name,
                "DR03": "不适用",
                "DR04": "不适用",
                "DR05": "不适用",
                "DR06": sheet_names[i] if '表' in sheet_names[i] else table_name,
                "DR07": upper_part(sheet_names[i] if '表' in sheet_names[i] else table_name)['表名ID'] if
                upper_part(sheet_names[i] if '表' in sheet_names[i] else table_name)['表名ID'] not in ['', None]
                else upper_part(requirement_doc_name)['表名ID'],
                "DR08": "不适用",
                "DR09": "报送范围",
                "DR10": "不适用",
                "DR11": "不适用",
                "DR12": "不适用",
                "DR13": "不适用",
                "DR14": "不适用",
                "DR15": "不适用",
                "DR16": "不适用",
                "DR17": processed_parts['第三部分'][i]['具体说明'],
                "DR18": "不适用",
                "DR19": "月报",
                "DR20": "不适用",
                "DR21": "不脱敏",
                "DR22": ""
            })

            table_name = sheet_names[i]
            out_dict[table_name] = output

            wb = Workbook()
            wb.remove(wb.active)

            for i, (sheet_name, _) in enumerate(project_names):
                sheet_name = clean_sheet_name(sheet_names[i], i)
                data = out_dict.get(sheet_name, [])
                if not data:
                    continue

                ws = wb.create_sheet(title=sheet_name)

                # 恢复样式（保持不变）
                for row_idx, row_data in enumerate(style_data["rows"], start=1):
                    for col_idx, cell_data in enumerate(row_data, start=1):
                        cell = ws.cell(row=row_idx, column=col_idx, value=cell_data["value"])

                        # 设置字体
                        font_info = cell_data.get("font", {})
                        cell.font = Font(
                            name=font_info.get("name", "Calibri"),
                            size=int(font_info.get("size", 11)),
                            bold=font_info.get("bold", False),
                            italic=font_info.get("italic", False),
                            color=font_info.get("color", "FF000000")
                        )

                        # 设置背景色
                        fill_info = cell_data.get("fill", {})
                        bg_color = fill_info.get("bgColor", "FFFFFFFF")
                        cell.fill = PatternFill(patternType="solid", fgColor=bg_color)

                        # 设置对齐
                        align_info = cell_data.get("alignment", {})
                        h_align = align_info.get("horizontal", "left")
                        v_align = align_info.get("vertical", "top")
                        wrap_text = align_info.get("wrap_text", False)
                        cell.alignment = Alignment(horizontal=h_align, vertical=v_align, wrap_text=wrap_text)

                        # -----------------------------
                        # ✅ 第二步：设置列宽
                        # -----------------------------
                    for col_letter, width in style_data.get("column_widths", {}).items():
                        ws.column_dimensions[col_letter].width = float(width)

                        # -----------------------------
                        # ✅ 第三步：设置行高
                        # -----------------------------
                    for row_number, height in style_data.get("row_heights", {}).items():
                        ws.row_dimensions[int(row_number)].height = float(height)

                        # -----------------------------
                        # ✅ 第四步：恢复合并单元格
                        # -----------------------------
                    for merged_range in style_data.get("merged_cells", []):
                        ws.merge_cells(merged_range)
                        # 字体、背景、对齐等设置（保持不变）

                # 写入数据
                df = pd.DataFrame(data)
                for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=False), start=5):
                    for c_idx, value in enumerate(row, start=1):
                        ws.cell(row=r_idx, column=c_idx, value=value)

            wb.save(excel_path)
            print(f"输出文件sheet {table_name} 已保存")

    except (IOError, ValueError, FileNotFoundError) as e:
        print(f"处理失败: {str(e)}")
        return ''

    return excel_path, tran_type


if __name__ == "__main__":
    file_path = input("请输入对应需要解析的doc文件路径：").strip()

    csv_dir = input("请输入对应需要解析的csv文件路径：").strip()
    sheet_names_input = input("请输入工作表名称（多个用逗号分隔，留空处理所有工作表）: ").strip().replace('，', ',')

    _, tt = execute_processing_logic(file_path, csv_dir, sheet_names_input)
    print(tt)
